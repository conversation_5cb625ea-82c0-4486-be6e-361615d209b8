#!/bin/bash

echo "========================================="
echo "ECR 前端服务器启动脚本"
echo "========================================="

# 检查Python是否安装
if command -v python3 &> /dev/null; then
    echo "✅ 使用Python3启动HTTP服务器"
    echo "🌐 前端地址: http://localhost:8000"
    echo "📱 在浏览器中打开上述地址"
    echo ""
    echo "按 Ctrl+C 停止服务器"
    echo ""
    python3 -m http.server 8000
elif command -v python &> /dev/null; then
    echo "✅ 使用Python启动HTTP服务器"
    echo "🌐 前端地址: http://localhost:8000"
    echo "📱 在浏览器中打开上述地址"
    echo ""
    echo "按 Ctrl+C 停止服务器"
    echo ""
    python -m SimpleHTTPServer 8000
elif command -v node &> /dev/null; then
    echo "✅ 使用Node.js启动HTTP服务器"
    echo "📦 安装http-server..."
    npx http-server -p 8000 -c-1
else
    echo "❌ 未找到Python或Node.js"
    echo "请安装Python或Node.js来启动本地服务器"
    echo ""
    echo "或者直接在浏览器中打开 index.html 文件"
    echo "（可能会遇到CORS问题）"
    exit 1
fi
