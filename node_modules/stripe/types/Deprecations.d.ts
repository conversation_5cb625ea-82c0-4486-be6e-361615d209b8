declare module 'stripe' {
  namespace Stripe {
    // TODO (MAJOR): go/j/DEVSDK-1812 remove the entire file
    /**
     * The resulting source of [a Connect platform debiting a connected account](https://stripe.com/docs/connect/account-debits#charging-a-connected-account).
     * @deprecated prefer Stripe.Account
     */
    type AccountDebitSource = Account;

    /**
     * @deprecated prefer {@link AccountListCapabilitiesParams}
     **/
    type CapabilityListParams = AccountListCapabilitiesParams;

    /**
     * @deprecated prefer {@link AccountRetrieveCapabilityParams}
     **/
    type CapabilityRetrieveParams = AccountRetrieveCapabilityParams;

    /**
     * @deprecated prefer {@link AccountUpdateCapabilityParams}
     **/
    type CapabilityUpdateParams = AccountUpdateCapabilityParams;

    /**
     * @deprecated prefer {@link CustomerRetrieveCashBalanceParams}
     **/
    type CashBalanceRetrieveParams = CustomerRetrieveCashBalanceParams;

    /**
     * @deprecated prefer {@link CustomerUpdateCashBalanceParams}
     **/
    type CashBalanceUpdateParams = CustomerUpdateCashBalanceParams;

    /**
     * @deprecated prefer {@link CreditNoteListLineItemsParams}
     **/
    type CreditNoteLineItemListParams = CreditNoteListLineItemsParams;

    /**
     * @deprecated prefer {@link CustomerCreateBalanceTransactionParams}
     **/
    type CustomerBalanceTransactionCreateParams = CustomerCreateBalanceTransactionParams;

    /**
     * @deprecated prefer {@link CustomerListBalanceTransactionsParams}
     **/
    type CustomerBalanceTransactionListParams = CustomerListBalanceTransactionsParams;

    /**
     * @deprecated prefer {@link CustomerRetrieveBalanceTransactionParams}
     **/
    type CustomerBalanceTransactionRetrieveParams = CustomerRetrieveBalanceTransactionParams;

    /**
     * @deprecated prefer {@link CustomerUpdateBalanceTransactionParams}
     **/
    type CustomerBalanceTransactionUpdateParams = CustomerUpdateBalanceTransactionParams;

    /**
     * @deprecated prefer {@link CustomerListCashBalanceTransactionsParams}
     **/
    type CustomerCashBalanceTransactionListParams = CustomerListCashBalanceTransactionsParams;

    /**
     * @deprecated prefer {@link CustomerRetrieveCashBalanceTransactionParams}
     **/
    type CustomerCashBalanceTransactionRetrieveParams = CustomerRetrieveCashBalanceTransactionParams;

    /**
     * @deprecated prefer {@link CustomerCreateSourceParams}
     **/
    type CustomerSourceCreateParams = CustomerCreateSourceParams;

    /**
     * @deprecated prefer {@link CustomerDeleteSourceParams}
     **/
    type CustomerSourceDeleteParams = CustomerDeleteSourceParams;

    /**
     * @deprecated prefer {@link CustomerListSourcesParams}
     **/
    type CustomerSourceListParams = CustomerListSourcesParams;

    /**
     * @deprecated prefer {@link CustomerRetrieveSourceParams}
     **/
    type CustomerSourceRetrieveParams = CustomerRetrieveSourceParams;

    /**
     * @deprecated prefer {@link CustomerUpdateSourceParams}
     **/
    type CustomerSourceUpdateParams = CustomerUpdateSourceParams;

    /**
     * @deprecated prefer {@link CustomerVerifySourceParams}
     **/
    type CustomerSourceVerifyParams = CustomerVerifySourceParams;

    /**
     * @deprecated prefer {@link AccountCreateExternalAccountParams}
     **/
    type ExternalAccountCreateParams = AccountCreateExternalAccountParams;

    /**
     * @deprecated prefer {@link AccountDeleteExternalAccountParams}
     **/
    type ExternalAccountDeleteParams = AccountDeleteExternalAccountParams;

    /**
     * @deprecated prefer {@link AccountListExternalAccountsParams}
     **/
    type ExternalAccountListParams = AccountListExternalAccountsParams;

    /**
     * @deprecated prefer {@link AccountRetrieveExternalAccountParams}
     **/
    type ExternalAccountRetrieveParams = AccountRetrieveExternalAccountParams;

    /**
     * @deprecated prefer {@link AccountUpdateExternalAccountParams}
     **/
    type ExternalAccountUpdateParams = AccountUpdateExternalAccountParams;

    /**
     * @deprecated prefer {@link ApplicationFeeCreateRefundParams}
     **/
    type FeeRefundCreateParams = ApplicationFeeCreateRefundParams;

    /**
     * @deprecated prefer {@link ApplicationFeeListRefundsParams}
     **/
    type FeeRefundListParams = ApplicationFeeListRefundsParams;

    /**
     * @deprecated prefer {@link ApplicationFeeRetrieveRefundParams}
     **/
    type FeeRefundRetrieveParams = ApplicationFeeRetrieveRefundParams;

    /**
     * @deprecated prefer {@link ApplicationFeeUpdateRefundParams}
     **/
    type FeeRefundUpdateParams = ApplicationFeeUpdateRefundParams;

    /**
     * @deprecated prefer {@link InvoiceListLineItemsParams}
     **/
    type InvoiceLineItemListParams = InvoiceListLineItemsParams;

    /**
     * @deprecated prefer {@link InvoiceUpdateLineItemParams}
     **/
    type InvoiceLineItemUpdateParams = InvoiceUpdateLineItemParams;

    /**
     * @deprecated prefer {@link AccountCreateLoginLinkParams}
     **/
    type LoginLinkCreateParams = AccountCreateLoginLinkParams;

    /**
     * @deprecated prefer {@link AccountCreatePersonParams}
     **/
    type PersonCreateParams = AccountCreatePersonParams;

    /**
     * @deprecated prefer {@link AccountDeletePersonParams}
     **/
    type PersonDeleteParams = AccountDeletePersonParams;

    /**
     * @deprecated prefer {@link AccountListPersonsParams}
     **/
    type PersonListParams = AccountListPersonsParams;

    /**
     * @deprecated prefer {@link AccountRetrievePersonParams}
     **/
    type PersonRetrieveParams = AccountRetrievePersonParams;

    /**
     * @deprecated prefer {@link AccountUpdatePersonParams}
     **/
    type PersonUpdateParams = AccountUpdatePersonParams;

    /**
     * @deprecated prefer {@link TransferCreateReversalParams}
     **/
    type TransferReversalCreateParams = TransferCreateReversalParams;

    /**
     * @deprecated prefer {@link TransferListReversalsParams}
     **/
    type TransferReversalListParams = TransferListReversalsParams;

    /**
     * @deprecated prefer {@link TransferRetrieveReversalParams}
     **/
    type TransferReversalRetrieveParams = TransferRetrieveReversalParams;

    /**
     * @deprecated prefer {@link TransferUpdateReversalParams}
     **/
    type TransferReversalUpdateParams = TransferUpdateReversalParams;

    /**
     * @deprecated prefer {@link SubscriptionItemCreateUsageRecordParams}
     **/
    type UsageRecordCreateParams = SubscriptionItemCreateUsageRecordParams;

    /**
     * @deprecated prefer {@link SubscriptionItemListUsageRecordSummariesParams}
     **/
    type UsageRecordSummaryListParams = SubscriptionItemListUsageRecordSummariesParams;
  }
}
