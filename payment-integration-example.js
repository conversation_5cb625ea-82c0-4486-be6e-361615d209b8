// Stripe Payment Links 集成示例
// 适用于 GitHub Pages 静态部署

class PaymentManager {
    constructor() {
        this.paymentLinkBase = 'https://buy.stripe.com/your-payment-link';
        this.successUrl = window.location.origin + '/payment-success.html';
        this.cancelUrl = window.location.origin + '/payment-cancel.html';
    }
    
    // 创建支付链接
    createPaymentLink(userResults) {
        const metadata = {
            anxietyScore: userResults.anxiety,
            avoidanceScore: userResults.avoidance,
            attachmentType: userResults.type,
            userId: this.generateUserId(),
            timestamp: Date.now()
        };
        
        const params = new URLSearchParams({
            'client_reference_id': JSON.stringify(metadata),
            'success_url': this.successUrl,
            'cancel_url': this.cancelUrl
        });
        
        return `${this.paymentLinkBase}?${params.toString()}`;
    }
    
    // 处理支付
    async processPayment(userResults) {
        try {
            const paymentUrl = this.createPaymentLink(userResults);
            
            // 保存用户结果到localStorage（支付成功后使用）
            localStorage.setItem('pendingResults', JSON.stringify(userResults));
            
            // 跳转到支付页面
            window.location.href = paymentUrl;
            
        } catch (error) {
            console.error('支付处理失败:', error);
            this.showError('支付处理失败，请重试');
        }
    }
    
    // 验证支付成功
    handlePaymentSuccess() {
        const urlParams = new URLSearchParams(window.location.search);
        const sessionId = urlParams.get('session_id');
        
        if (sessionId) {
            // 获取保存的用户结果
            const userResults = JSON.parse(localStorage.getItem('pendingResults') || '{}');
            
            // 清除临时数据
            localStorage.removeItem('pendingResults');
            
            // 显示详细报告
            this.showDetailedReport(userResults);
            
            return true;
        }
        
        return false;
    }
    
    // 生成用户ID
    generateUserId() {
        return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    // 显示错误信息
    showError(message) {
        // 实现错误提示UI
        alert(message); // 简单实现，可以替换为更好的UI
    }
    
    // 显示详细报告
    showDetailedReport(userResults) {
        // 实现详细报告显示逻辑
        console.log('显示详细报告:', userResults);
        // 这里可以调用现有的报告显示函数
    }
}

// 使用示例
const paymentManager = new PaymentManager();

// 在支付按钮点击时调用
document.getElementById('paymentBtn')?.addEventListener('click', function() {
    const userResults = getCurrentTestResults(); // 获取当前测评结果
    paymentManager.processPayment(userResults);
});

// 在支付成功页面调用
if (window.location.pathname.includes('payment-success')) {
    paymentManager.handlePaymentSuccess();
}

// ===== Vercel Functions 方案示例 =====

// 如果选择使用 Vercel Functions，以下是API函数示例：

/*
// api/create-payment-intent.js (部署到 Vercel)
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

export default async function handler(req, res) {
    // 设置CORS头
    res.setHeader('Access-Control-Allow-Origin', 'https://yourusername.github.io');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }
    
    if (req.method === 'POST') {
        try {
            const { amount = 1990, currency = 'cny', metadata } = req.body;
            
            const paymentIntent = await stripe.paymentIntents.create({
                amount: amount,
                currency: currency,
                metadata: metadata,
                automatic_payment_methods: {
                    enabled: true,
                },
            });
            
            res.json({
                success: true,
                clientSecret: paymentIntent.client_secret,
                paymentIntentId: paymentIntent.id
            });
            
        } catch (error) {
            console.error('创建支付意图失败:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    } else {
        res.status(405).json({ error: 'Method not allowed' });
    }
}
*/

// 前端调用 Vercel API 的示例
class VercelPaymentManager {
    constructor() {
        this.apiBase = 'https://your-vercel-app.vercel.app/api';
        this.stripe = Stripe('pk_test_your_publishable_key'); // 替换为你的公钥
    }
    
    async createPaymentIntent(orderData) {
        try {
            const response = await fetch(`${this.apiBase}/create-payment-intent`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(orderData)
            });
            
            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.error);
            }
            
            return data;
            
        } catch (error) {
            console.error('创建支付意图失败:', error);
            throw error;
        }
    }
    
    async processPayment(userResults) {
        try {
            // 创建支付意图
            const { clientSecret } = await this.createPaymentIntent({
                amount: 1990, // 19.90元
                metadata: {
                    anxietyScore: userResults.anxiety,
                    avoidanceScore: userResults.avoidance,
                    attachmentType: userResults.type
                }
            });
            
            // 使用 Stripe Elements 处理支付
            const { error } = await this.stripe.confirmPayment({
                clientSecret,
                confirmParams: {
                    return_url: window.location.origin + '/payment-success.html'
                }
            });
            
            if (error) {
                throw error;
            }
            
        } catch (error) {
            console.error('支付处理失败:', error);
            this.showError('支付处理失败: ' + error.message);
        }
    }
    
    showError(message) {
        alert(message); // 可以替换为更好的UI
    }
}

// 使用 Vercel 方案的示例
// const vercelPaymentManager = new VercelPaymentManager();
