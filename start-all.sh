#!/bin/bash

echo "========================================="
echo "ECR Stripe支付系统完整启动脚本"
echo "========================================="

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，请先安装Node.js"
    exit 1
fi

# 安装依赖
echo "📦 检查并安装依赖..."
npm install

# 启动后端服务器（后台运行）
echo "🚀 启动后端服务器..."
npm start &
BACKEND_PID=$!

# 等待后端启动
echo "⏳ 等待后端服务器启动..."
sleep 3

# 检查后端是否启动成功
if curl -s http://localhost:3001/api/health > /dev/null; then
    echo "✅ 后端服务器启动成功 (PID: $BACKEND_PID)"
    echo "🔗 后端地址: http://localhost:3001"
else
    echo "❌ 后端服务器启动失败"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

# 启动前端服务器
echo ""
echo "🌐 启动前端服务器..."
echo "📱 前端地址: http://localhost:8000"
echo ""
echo "========================================="
echo "🎉 系统启动完成！"
echo "========================================="
echo "后端API: http://localhost:3001"
echo "前端页面: http://localhost:8000"
echo ""
echo "请在浏览器中打开: http://localhost:8000"
echo ""
echo "按 Ctrl+C 停止所有服务器"
echo "========================================="
echo ""

# 定义清理函数
cleanup() {
    echo ""
    echo "🛑 正在停止服务器..."
    kill $BACKEND_PID 2>/dev/null
    echo "✅ 所有服务器已停止"
    exit 0
}

# 捕获中断信号
trap cleanup SIGINT SIGTERM

# 启动前端服务器
if command -v python3 &> /dev/null; then
    python3 -m http.server 8000
elif command -v python &> /dev/null; then
    python -m SimpleHTTPServer 8000
else
    npx http-server -p 8000 -c-1
fi
